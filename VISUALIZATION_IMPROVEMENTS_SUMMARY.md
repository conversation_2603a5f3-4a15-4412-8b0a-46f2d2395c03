# AI对冲基金系统LLM模型代理准确率分析工具 - 可视化改进总结

## 问题描述

用户反馈生成的图表 `accuracy_comparison_AAPL.png` 中数字标签过于靠近，导致部分重叠，影响图表的可读性。

## 解决方案

### 1. 增加图表尺寸

**修改前**: 默认图表大小 15x10
**修改后**: 默认图表大小 18x12

```python
# 修改函数签名
def plot_accuracy_comparison(self, ticker: str, save_path: Optional[str] = None, 
                           figsize: Tuple[int, int] = (18, 12)) -> None:

# 修改命令行参数默认值
parser.add_argument('--figsize', type=str, default='18,12',
                   help='图表大小，格式: width,height (默认: 18,12)')
```

### 2. 动态调整柱子间距

根据模型数量动态调整柱子宽度和组间距，避免柱子过于密集：

```python
# 根据代理和模型数量动态调整柱子宽度和间距
if n_models <= 3:
    bar_width = 0.7 / n_models
    group_spacing = 1.2
elif n_models <= 5:
    bar_width = 0.6 / n_models
    group_spacing = 1.5
else:
    bar_width = 0.5 / n_models
    group_spacing = 2.0
    
x = np.arange(n_agents) * group_spacing
```

### 3. 智能标签显示策略

根据模型数量和柱子高度智能决定是否显示标签：

```python
# 动态调整字体大小和标签显示策略
if n_models <= 3:
    fontsize = 8
    show_labels = True                    # 显示所有标签
elif n_models <= 5:
    fontsize = 7
    show_labels = height >= 0.05         # 只显示较高的柱子标签
else:
    fontsize = 6
    show_labels = height >= 0.1          # 只显示最高的柱子标签
```

### 4. 优化标签位置

改进标签位置计算，避免重叠：

```python
if show_labels:
    # 计算标签位置，避免重叠
    label_height = height + 0.015
    
    # 如果柱子太低，将标签放在柱子内部
    if height < 0.15:
        label_height = height / 2
        va_align = 'center'
        color = 'white'
        fontweight = 'bold'
    else:
        va_align = 'bottom'
        color = 'black'
        fontweight = 'normal'
    
    # 显示数值标签
    ax.text(bar.get_x() + bar.get_width()/2., label_height,
           f'{height:.2f}', ha='center', va=va_align, 
           fontsize=fontsize, color=color, fontweight=fontweight)
```

### 5. 改进数值精度

将数值显示精度从3位小数改为2位小数，减少标签宽度：

**修改前**: `f'{height:.3f}'`
**修改后**: `f'{height:.2f}'`

### 6. 优化X轴标签

增加X轴标签的字体大小设置：

```python
ax.set_xticklabels(agent_labels, rotation=45, ha='right', fontsize=10)
```

## 改进效果

### 不同模型数量的适应性

1. **≤3个模型**: 
   - 字体大小: 8
   - 显示所有标签
   - 较宽的柱子间距

2. **4-5个模型**:
   - 字体大小: 7
   - 只显示准确率≥0.05的标签
   - 中等柱子间距

3. **>5个模型**:
   - 字体大小: 6
   - 只显示准确率≥0.1的标签
   - 更宽的柱子间距

### 标签位置优化

- **高柱子** (≥0.15): 标签显示在柱子上方，黑色文字
- **低柱子** (<0.15): 标签显示在柱子中央，白色粗体文字

### 图表质量提升

- **更大的画布**: 18x12 英寸，提供更多空间
- **更好的间距**: 动态调整避免拥挤
- **更清晰的标签**: 智能显示策略减少视觉混乱
- **更高的可读性**: 优化的字体大小和颜色对比

## 测试验证

通过测试不同场景验证改进效果：

### 测试场景

1. **AAPL (3个模型)**: 2025年数据
   - ✅ 所有标签清晰显示
   - ✅ 无重叠问题
   - ✅ 图表大小适中

2. **NVDA (5个模型)**: 所有数据
   - ✅ 智能标签过滤生效
   - ✅ 柱子间距合理
   - ✅ 可读性良好

3. **MSFT (2个模型)**: 2024年数据
   - ✅ 标签显示完整
   - ✅ 布局清晰

### 文件大小

生成的图片文件大小合理：
- AAPL: 386.6 KB
- NVDA: 414.5 KB  
- MSFT: 314.3 KB

## 向后兼容性

所有改进都保持了向后兼容性：

- ✅ 现有的命令行参数仍然有效
- ✅ 图表格式和内容保持一致
- ✅ 输出文件路径和命名不变
- ✅ 用户可以通过 `--figsize` 参数自定义图表大小

## 总结

通过这些改进，成功解决了数字标签重叠的问题：

1. **空间优化**: 增加图表大小，提供更多显示空间
2. **智能布局**: 动态调整柱子间距和标签显示策略
3. **可读性提升**: 优化字体大小、颜色和位置
4. **适应性增强**: 根据数据密度自动调整显示策略

现在生成的图表在各种数据密度下都能保持良好的可读性，有效避免了标签重叠问题。
