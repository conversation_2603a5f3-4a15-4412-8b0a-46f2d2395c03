# AI对冲基金系统LLM模型代理准确率分析工具

## 概述

这个Python脚本用于分析和可视化AI对冲基金系统中不同LLM模型的代理准确率表现。它可以从多个reasoning_logs文件夹中读取final_accuracy_report文件，生成分组柱状图展示不同模型下各代理的准确率对比。

## 功能特性

- **多模型对比**: 支持同时分析多个LLM模型的表现
- **多股票支持**: 可以分析不同股票（AAPL、NVDA、MSFT等）的数据
- **日期范围过滤**: 支持按日期范围过滤数据，默认分析2025年1月1日至6月1日的数据
- **可视化图表**: 生成高质量的分组柱状图，支持中文显示
- **详细报告**: 生成包含统计分析的文本报告
- **数据导出**: 支持CSV格式数据导出
- **灵活配置**: 支持命令行参数配置

## 安装依赖

```bash
pip install pandas matplotlib numpy pathlib
```

## 使用方法

### 基本用法

```bash
# 分析AAPL股票的数据
python llm_accuracy_analyzer.py --ticker AAPL

# 分析NVDA股票的数据
python llm_accuracy_analyzer.py --ticker NVDA
```

### 高级用法

```bash
# 列出所有可用的股票代码和模型
python llm_accuracy_analyzer.py --list-available

# 分析所有可用的股票
python llm_accuracy_analyzer.py --all-tickers

# 指定日期范围过滤（默认为20250101-20250601）
python llm_accuracy_analyzer.py --ticker AAPL --date-range 20240102-20241231

# 不进行日期过滤，分析所有可用数据
python llm_accuracy_analyzer.py --ticker AAPL --no-date-filter

# 指定输出目录和图表大小
python llm_accuracy_analyzer.py --ticker AAPL --output-dir my_analysis --figsize 20,12

# 指定reasoning_logs路径
python llm_accuracy_analyzer.py --ticker AAPL --base-path /path/to/reasoning_logs

# 组合使用多个参数
python llm_accuracy_analyzer.py --all-tickers --date-range 20250101-20250601 --output-dir filtered_analysis
```

### 命令行参数

- `--ticker, -t`: 目标股票代码 (默认: AAPL)
- `--base-path, -p`: reasoning_logs文件夹路径 (默认: reasoning_logs)
- `--output-dir, -o`: 输出文件夹路径 (默认: accuracy_analysis_output)
- `--figsize`: 图表大小，格式: width,height (默认: 15,10)
- `--all-tickers`: 分析所有可用的股票代码
- `--list-available`: 列出所有可用的股票代码和模型
- `--date-range`: 日期范围过滤，格式: YYYYMMDD-YYYYMMDD (默认: 20250101-20250601)
- `--no-date-filter`: 不进行日期范围过滤，分析所有可用数据

## 日期范围过滤功能

脚本支持按日期范围过滤数据文件夹，这对于分析特定时间段的模型表现非常有用。

### 过滤规则

- **默认行为**: 脚本默认只分析日期范围为 "20250101-20250601" 的数据文件夹
- **匹配模式**: 只处理文件夹名称中包含指定日期范围的文件夹
  - `accuracy_tracking_{TICKER}_20250101-20250601_{MODEL}`
  - `experiment_{TICKER}_20250101-20250601_{MODEL}`

### 使用示例

```bash
# 分析2025年1-6月的数据（默认）
python llm_accuracy_analyzer.py --ticker AAPL

# 分析2024年全年的数据
python llm_accuracy_analyzer.py --ticker AAPL --date-range 20240102-20241231

# 分析所有可用数据，不进行日期过滤
python llm_accuracy_analyzer.py --ticker AAPL --no-date-filter

# 查看特定日期范围内的可用数据
python llm_accuracy_analyzer.py --list-available --date-range 20240102-20241231
```

### 注意事项

- 日期格式必须严格按照 `YYYYMMDD-YYYYMMDD` 格式
- 如果指定的日期范围没有对应的数据文件夹，将不会有任何数据被加载
- 使用 `--no-date-filter` 参数可以分析所有可用的数据，不受日期限制

## 输出文件

脚本会在指定的输出目录中生成以下文件：

1. **可视化图表**: `accuracy_comparison_{TICKER}.png`
   - 分组柱状图显示各代理在不同模型下的准确率
   - 支持中文标签和图例
   - 高分辨率PNG格式

2. **分析报告**: `accuracy_report_{TICKER}.txt`
   - 整体统计信息
   - 各模型平均准确率排名
   - 各代理平均准确率排名
   - 最佳模型-代理组合Top 5

3. **原始数据**: `accuracy_data_{TICKER}.csv`
   - CSV格式的原始准确率数据
   - 可用于进一步分析或导入其他工具

## 数据格式要求

脚本期望在reasoning_logs文件夹中找到以下格式的文件夹：

```
reasoning_logs/
├── accuracy_tracking_{TICKER}_{DATERANGE}_{MODEL}/
│   └── final_accuracy_report_experiment_{DATE}_{TICKER}.json
└── experiment_{TICKER}_{DATERANGE}_{MODEL}/
    └── final_accuracy_report_experiment_{DATE}_{TICKER}.json
```

JSON文件应包含`cumulative_stats`字段，其中包含各代理的`accuracy_rate`数据。

## 示例输出

### 分析报告示例

```
=== AAPL 股票LLM模型代理准确率分析报告 ===

1. 整体统计:
   - 分析的模型数量: 5
   - 分析的代理数量: 20
   - 平均准确率: 0.3778

2. 各模型平均准确率:
   - deepseekv3: 0.4354
   - LLAMA: 0.4254
   - grok-beta: 0.3494
   - gemini2.0-flash: 0.3449
   - gpt3.5: 0.3342

3. 各代理平均准确率:
   - cathie wood: 0.5505
   - charlie munger: 0.5387
   - phil fisher: 0.5074
   ...

4. 最佳模型-代理组合 (Top 5):
   1. phil fisher + deepseekv3: 0.6308
   2. bill ackman + deepseekv3: 0.6256
   ...
```

## 支持的模型

当前支持的LLM模型包括：
- DeepSeek v3
- LLAMA
- Gemini 2.0 Flash
- GPT-3.5
- Grok Beta
- LLAMA4 Scout

## 支持的代理

脚本可以分析以下类型的代理：
- 基础分析代理 (fundamentals_agent)
- 技术分析代理 (technical_analyst_agent)
- 新闻分析代理 (factual_news_agent, subjective_news_agent)
- 情感分析代理 (sentiment_agent)
- 社交媒体分析代理 (social_media_analyst_agent)
- 投资大师代理 (warren_buffett_agent, cathie_wood_agent等)

## 故障排除

### 常见问题

1. **JSON解析错误**: 如果遇到"Extra data"错误，通常是因为JSON文件末尾有注释。脚本已经处理了这种情况。

2. **找不到文件**: 确保reasoning_logs文件夹路径正确，且包含正确格式的子文件夹。

3. **中文显示问题**: 脚本已配置中文字体支持，如果仍有问题，请检查系统字体安装。

4. **内存不足**: 对于大量数据，可能需要增加系统内存或分批处理。

### 调试模式

脚本包含详细的错误处理和日志输出，可以帮助诊断问题：

```bash
python llm_accuracy_analyzer.py --ticker AAPL --list-available
```

## 贡献

欢迎提交问题报告和功能请求。如需贡献代码，请确保：
1. 代码符合PEP 8规范
2. 添加适当的注释和文档
3. 测试新功能

## 许可证

本项目采用MIT许可证。

## 更新日志

### v1.0.0 (2025-07-07)
- 初始版本发布
- 支持多模型、多股票分析
- 生成可视化图表和详细报告
- 支持CSV数据导出
- 完整的命令行界面
